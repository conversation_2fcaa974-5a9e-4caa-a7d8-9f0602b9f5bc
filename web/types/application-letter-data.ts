/**
 * Structured data types for application letter generation
 */

export interface ApplicantInfo {
  fullName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
}

export interface JobApplication {
  position: string;
  company: string;
  department?: string;
  jobSource: string; // Where they found the job (website, referral, etc.)
  date: string; // Application date
}

export interface WorkExperience {
  jobTitle: string;
  company: string;
  duration: string;
  achievements: string[];
}

export interface Education {
  degree: string;
  institution: string;
  graduationYear: string;
  gpa?: string;
}

export interface Skills {
  technical: string[];
  soft: string[];
  languages: string[];
}

export interface ApplicationLetterData {
  applicant: ApplicantInfo;
  job: JobApplication;
  experience: WorkExperience[];
  education: Education[];
  skills: Skills;
  motivation: string; // Why they want to work at the company
  qualifications: string; // Key qualifications summary
  closingStatement: string; // Closing paragraph
  attachments: string[]; // List of attached documents
}

/**
 * Template data validation interface
 */
export interface LetterTemplateValidation {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
}

/**
 * Template filling options
 */
export interface LetterTemplateOptions {
  includeAttachments?: boolean;
  dateFormat?: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
  language?: 'id' | 'en';
}

/**
 * Validate application letter data
 * @param data - Application letter data to validate
 * @returns Validation result
 */
export function validateApplicationLetterData(data: ApplicationLetterData): LetterTemplateValidation {
  const missingFields: string[] = [];
  const warnings: string[] = [];

  // Required applicant fields
  if (!data.applicant.fullName) missingFields.push('applicant.fullName');
  if (!data.applicant.email) missingFields.push('applicant.email');
  if (!data.applicant.phone) missingFields.push('applicant.phone');
  if (!data.applicant.address) missingFields.push('applicant.address');
  if (!data.applicant.city) missingFields.push('applicant.city');

  // Required job fields
  if (!data.job.position) missingFields.push('job.position');
  if (!data.job.company) missingFields.push('job.company');
  if (!data.job.date) missingFields.push('job.date');

  // Required content fields
  if (!data.motivation) missingFields.push('motivation');
  if (!data.qualifications) missingFields.push('qualifications');
  if (!data.closingStatement) missingFields.push('closingStatement');

  // Warnings for missing optional but recommended fields
  if (!data.job.department) warnings.push('job.department is recommended');
  if (!data.job.jobSource) warnings.push('job.jobSource is recommended');
  if (data.experience.length === 0) warnings.push('At least one work experience entry is recommended');
  if (data.education.length === 0) warnings.push('At least one education entry is recommended');

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings
  };
}

/**
 * Create empty application letter structure
 */
export function createEmptyApplicationLetterData(): ApplicationLetterData {
  return {
    applicant: {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      postalCode: ''
    },
    job: {
      position: '',
      company: '',
      department: '',
      jobSource: '',
      date: new Date().toLocaleDateString('id-ID')
    },
    experience: [],
    education: [],
    skills: {
      technical: [],
      soft: [],
      languages: []
    },
    motivation: '',
    qualifications: '',
    closingStatement: '',
    attachments: ['CV/Resume', 'Ijazah', 'Transkrip Nilai']
  };
}
