'use client';

import { useState } from 'react';
import { ApplicationLetterData, createEmptyApplicationLetterData } from '@/types/application-letter-data';

interface ApplicationLetterFormProps {
  data: ApplicationLetterData;
  onChange: (data: ApplicationLetterData) => void;
}

export default function ApplicationLetterForm({ data, onChange }: ApplicationLetterFormProps) {
  const handleInputChange = (
    section: keyof ApplicationLetterData,
    field: string,
    value: string | string[]
  ) => {
    const updatedData = { ...data };
    
    if (section === 'applicant' || section === 'job') {
      (updatedData[section] as any)[field] = value;
    } else if (section === 'skills') {
      if (Array.isArray(value)) {
        (updatedData.skills as any)[field] = value;
      } else {
        // Handle comma-separated string input
        (updatedData.skills as any)[field] = value.split(',').map(s => s.trim()).filter(s => s);
      }
    } else {
      (updatedData as any)[field] = value;
    }
    
    onChange(updatedData);
  };

  const handleAddExperience = () => {
    const newExperience = {
      jobTitle: '',
      company: '',
      duration: '',
      achievements: []
    };
    onChange({
      ...data,
      experience: [...data.experience, newExperience]
    });
  };

  const handleUpdateExperience = (index: number, field: string, value: string | string[]) => {
    const updatedExperience = [...data.experience];
    if (field === 'achievements') {
      updatedExperience[index].achievements = Array.isArray(value) 
        ? value 
        : value.split(',').map(s => s.trim()).filter(s => s);
    } else {
      (updatedExperience[index] as any)[field] = value;
    }
    onChange({
      ...data,
      experience: updatedExperience
    });
  };

  const handleRemoveExperience = (index: number) => {
    const updatedExperience = data.experience.filter((_, i) => i !== index);
    onChange({
      ...data,
      experience: updatedExperience
    });
  };

  const handleAddEducation = () => {
    const newEducation = {
      degree: '',
      institution: '',
      graduationYear: '',
      gpa: ''
    };
    onChange({
      ...data,
      education: [...data.education, newEducation]
    });
  };

  const handleUpdateEducation = (index: number, field: string, value: string) => {
    const updatedEducation = [...data.education];
    (updatedEducation[index] as any)[field] = value;
    onChange({
      ...data,
      education: updatedEducation
    });
  };

  const handleRemoveEducation = (index: number) => {
    const updatedEducation = data.education.filter((_, i) => i !== index);
    onChange({
      ...data,
      education: updatedEducation
    });
  };

  return (
    <div className="space-y-8">
      {/* Applicant Information */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Informasi Pribadi</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nama Lengkap *
            </label>
            <input
              type="text"
              value={data.applicant.fullName}
              onChange={(e) => handleInputChange('applicant', 'fullName', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Nama lengkap Anda"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <input
              type="email"
              value={data.applicant.email}
              onChange={(e) => handleInputChange('applicant', 'email', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nomor Telepon *
            </label>
            <input
              type="tel"
              value={data.applicant.phone}
              onChange={(e) => handleInputChange('applicant', 'phone', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="+62 812 3456 7890"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Kota *
            </label>
            <input
              type="text"
              value={data.applicant.city}
              onChange={(e) => handleInputChange('applicant', 'city', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Jakarta"
            />
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Alamat Lengkap *
            </label>
            <input
              type="text"
              value={data.applicant.address}
              onChange={(e) => handleInputChange('applicant', 'address', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Jl. Contoh No. 123"
            />
          </div>
        </div>
      </div>

      {/* Job Information */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Informasi Pekerjaan</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Posisi yang Dilamar *
            </label>
            <input
              type="text"
              value={data.job.position}
              onChange={(e) => handleInputChange('job', 'position', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Software Developer"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nama Perusahaan *
            </label>
            <input
              type="text"
              value={data.job.company}
              onChange={(e) => handleInputChange('job', 'company', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="PT. Contoh Indonesia"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Departemen
            </label>
            <input
              type="text"
              value={data.job.department || ''}
              onChange={(e) => handleInputChange('job', 'department', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Engineering Department"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sumber Lowongan
            </label>
            <input
              type="text"
              value={data.job.jobSource || ''}
              onChange={(e) => handleInputChange('job', 'jobSource', e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="LinkedIn, JobStreet, Website perusahaan"
            />
          </div>
        </div>
      </div>

      {/* Letter Content */}
      <div className="bg-white p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4">Isi Surat Lamaran</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Motivasi & Alasan Melamar *
            </label>
            <textarea
              value={data.motivation}
              onChange={(e) => handleInputChange('motivation' as any, '', e.target.value)}
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Berdasarkan informasi lowongan pekerjaan yang saya peroleh, saya ingin mengajukan diri untuk mengisi posisi..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Kualifikasi & Keahlian *
            </label>
            <textarea
              value={data.qualifications}
              onChange={(e) => handleInputChange('qualifications' as any, '', e.target.value)}
              rows={4}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Saya memiliki pengalaman dalam... dengan keahlian..."
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Penutup *
            </label>
            <textarea
              value={data.closingStatement}
              onChange={(e) => handleInputChange('closingStatement' as any, '', e.target.value)}
              rows={3}
              className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Besar harapan saya untuk dapat diberikan kesempatan wawancara..."
            />
          </div>
        </div>
      </div>

      {/* Experience Section */}
      <div className="bg-white p-6 rounded-lg border">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Pengalaman Kerja</h3>
          <button
            type="button"
            onClick={handleAddExperience}
            className="bg-primary hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm"
          >
            + Tambah Pengalaman
          </button>
        </div>
        {data.experience.map((exp, index) => (
          <div key={index} className="border-l-4 border-primary pl-4 mb-6 last:mb-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Jabatan
                </label>
                <input
                  type="text"
                  value={exp.jobTitle}
                  onChange={(e) => handleUpdateExperience(index, 'jobTitle', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Software Developer"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Perusahaan
                </label>
                <input
                  type="text"
                  value={exp.company}
                  onChange={(e) => handleUpdateExperience(index, 'company', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="PT. Contoh"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Durasi
                </label>
                <input
                  type="text"
                  value={exp.duration}
                  onChange={(e) => handleUpdateExperience(index, 'duration', e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Jan 2020 - Des 2022"
                />
              </div>
              <div className="flex items-end">
                <button
                  type="button"
                  onClick={() => handleRemoveExperience(index)}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm h-fit"
                >
                  Hapus
                </button>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pencapaian (pisahkan dengan koma)
              </label>
              <textarea
                value={exp.achievements.join(', ')}
                onChange={(e) => handleUpdateExperience(index, 'achievements', e.target.value)}
                rows={2}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Meningkatkan efisiensi sistem 30%, Memimpin tim 5 orang"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
