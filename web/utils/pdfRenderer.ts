import { createClient } from '@/lib/supabase';

/**
 * <PERSON><PERSON> provided HTML string to a PDF buffer using Supabase Edge Function with <PERSON><PERSON> and browserless.io.
 * This replaces the old Puppeteer implementation with a more scalable cloud-based solution.
 * Callers are responsible for catching any errors.
 */
export async function renderHtmlToPdf(html: string, fileName: string = 'document'): Promise<Uint8Array> {
  const supabase = createClient();

  // Get the current user's session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();

  if (sessionError || !session) {
    throw new Error('Authentication required for PDF generation');
  }

  // Call the Supabase Edge Function to generate PDF
  const { data: pdfData, error: edgeFunctionError } = await supabase.functions.invoke('html-to-pdf', {
    body: {
      html: html,
      fileName: fileName,
    },
    headers: {
      Authorization: `Bearer ${session.access_token}`,
    },
  });

  if (edgeFunctionError) {
    console.error('Edge Function error:', edgeFunctionError);
    throw new Error(`Failed to generate PDF: ${edgeFunctionError.message || 'Unknown error'}`);
  }

  if (!pdfData) {
    throw new Error('No PDF data received from Edge Function');
  }

  // Convert the response to Uint8Array
  if (pdfData instanceof ArrayBuffer) {
    return new Uint8Array(pdfData);
  } else if (pdfData instanceof Uint8Array) {
    return pdfData;
  } else {
    // If it's a Blob or other format, convert it
    const arrayBuffer = await new Response(pdfData).arrayBuffer();
    return new Uint8Array(arrayBuffer);
  }
}
