import { LetterTemplate } from './letter-templates/applicationLetterTemplates';
import { ApplicationLetterData, validateApplicationLetterData, LetterTemplateOptions } from '../types/application-letter-data';

/**
 * Letter Template Engine
 * Programmatically fills application letter templates with structured data
 */

/**
 * Simple template placeholder replacement engine
 * Uses {{key}} syntax for placeholders
 */
class SimpleTemplateEngine {
  /**
   * Replace placeholders in template string with data values
   * @param template - Template string with {{key}} placeholders
   * @param data - Data object to fill placeholders
   * @returns Filled template string
   */
  static fill(template: string, data: Record<string, any>): string {
    return template.replace(/\{\{([^}]+)\}\}/g, (match, key) => {
      const value = this.getNestedValue(data, key.trim());
      return value !== undefined ? String(value) : match;
    });
  }

  /**
   * Get nested object value using dot notation
   * @param obj - Object to get value from
   * @param path - Dot notation path (e.g., 'applicant.fullName')
   * @returns Value at path or undefined
   */
  private static getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }
}

/**
 * Format application letter data for template filling
 * @param data - Structured application letter data
 * @param options - Template options
 * @returns Formatted data object for template placeholders
 */
function formatLetterDataForTemplate(data: ApplicationLetterData, options: LetterTemplateOptions = {}): Record<string, any> {
  const {
    dateFormat = 'DD/MM/YYYY',
    includeAttachments = true,
    language = 'id'
  } = options;

  // Format date
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    if (dateFormat === 'DD/MM/YYYY') {
      return date.toLocaleDateString('id-ID');
    } else if (dateFormat === 'MM/DD/YYYY') {
      return date.toLocaleDateString('en-US');
    } else {
      return date.toISOString().split('T')[0];
    }
  };

  // Format skills as comma-separated strings
  const formatSkills = (skillArray: string[]): string => {
    return skillArray.join(', ');
  };

  // Format experience list
  const formatExperience = (experiences: typeof data.experience): string => {
    if (experiences.length === 0) return '';
    
    return experiences.map(exp => {
      const achievements = exp.achievements.length > 0 
        ? ` dengan pencapaian: ${exp.achievements.join(', ')}`
        : '';
      return `${exp.jobTitle} di ${exp.company} (${exp.duration})${achievements}`;
    }).join('\n');
  };

  // Format education list
  const formatEducation = (educations: typeof data.education): string => {
    if (educations.length === 0) return '';
    
    return educations.map(edu => {
      const gpaText = edu.gpa ? ` dengan IPK ${edu.gpa}` : '';
      return `${edu.degree} dari ${edu.institution} (${edu.graduationYear})${gpaText}`;
    }).join('\n');
  };

  // Format attachments list
  const formatAttachments = (attachments: string[]): string => {
    if (!includeAttachments || attachments.length === 0) return '';
    return attachments.join(', ');
  };

  return {
    // Applicant information
    'applicant.fullName': data.applicant.fullName,
    'applicant.email': data.applicant.email,
    'applicant.phone': data.applicant.phone,
    'applicant.address': data.applicant.address,
    'applicant.city': data.applicant.city,
    'applicant.postalCode': data.applicant.postalCode,
    'applicant.fullAddress': `${data.applicant.address}, ${data.applicant.city} ${data.applicant.postalCode}`,

    // Job information
    'job.position': data.job.position,
    'job.company': data.job.company,
    'job.department': data.job.department || '',
    'job.jobSource': data.job.jobSource || '',
    'job.date': formatDate(data.job.date),

    // Content
    'content.motivation': data.motivation,
    'content.qualifications': data.qualifications,
    'content.closingStatement': data.closingStatement,

    // Formatted sections
    'formatted.experience': formatExperience(data.experience),
    'formatted.education': formatEducation(data.education),
    'formatted.technicalSkills': formatSkills(data.skills.technical),
    'formatted.softSkills': formatSkills(data.skills.soft),
    'formatted.languages': formatSkills(data.skills.languages),
    'formatted.allSkills': formatSkills([
      ...data.skills.technical,
      ...data.skills.soft,
      ...data.skills.languages
    ]),
    'formatted.attachments': formatAttachments(data.attachments),

    // Date and current context
    'current.date': formatDate(new Date().toISOString()),
    'current.year': new Date().getFullYear().toString(),

    // Language-specific labels
    'labels.position': language === 'id' ? 'Posisi' : 'Position',
    'labels.company': language === 'id' ? 'Perusahaan' : 'Company',
    'labels.date': language === 'id' ? 'Tanggal' : 'Date',
    'labels.attachments': language === 'id' ? 'Lampiran' : 'Attachments',
    'labels.regards': language === 'id' ? 'Hormat saya' : 'Sincerely',
  };
}

/**
 * Main function to fill application letter template with structured data
 * @param template - Letter template object
 * @param data - Structured application letter data
 * @param options - Template filling options
 * @returns Filled HTML string
 * @throws Error if template filling fails or data validation fails
 */
export function fillLetterTemplate(
  template: LetterTemplate,
  data: ApplicationLetterData,
  options: LetterTemplateOptions = {}
): string {
  try {
    // Validate data
    const validation = validateApplicationLetterData(data);
    if (!validation.isValid) {
      throw new Error(`Invalid application letter data: ${validation.missingFields.join(', ')}`);
    }

    // Format data for template
    const templateData = formatLetterDataForTemplate(data, options);

    // Fill template with data
    const filledHtml = SimpleTemplateEngine.fill(template.templateHtml, templateData);

    // Validate that all required placeholders were filled
    const remainingPlaceholders = filledHtml.match(/\{\{[^}]+\}\}/g);
    if (remainingPlaceholders && remainingPlaceholders.length > 0) {
      console.warn('Some placeholders were not filled:', remainingPlaceholders);
    }

    return filledHtml;
  } catch (error) {
    console.error('Error filling letter template:', error);
    throw new Error(`Failed to fill letter template: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Test letter template compilation with sample data
 * @param template - Template to test
 * @param sampleData - Sample structured letter data
 * @returns Test result
 */
export function testLetterTemplate(template: LetterTemplate, sampleData: ApplicationLetterData): {
  success: boolean;
  error?: string;
  warnings?: string[];
  htmlLength?: number;
} {
  try {
    const validation = validateApplicationLetterData(sampleData);
    const warnings = validation.warnings;

    const filledHtml = fillLetterTemplate(template, sampleData);
    
    return {
      success: true,
      warnings,
      htmlLength: filledHtml.length
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get all placeholders used in a template
 * @param templateHtml - Template HTML string
 * @returns Array of placeholder keys
 */
export function getTemplatePlaceholders(templateHtml: string): string[] {
  const placeholders = templateHtml.match(/\{\{([^}]+)\}\}/g) || [];
  return placeholders.map(p => p.replace(/[{}]/g, '').trim());
}

/**
 * Check if template has required placeholders
 * @param template - Template to check
 * @param requiredPlaceholders - Array of required placeholder keys
 * @returns Validation result
 */
export function validateTemplateStructure(template: LetterTemplate, requiredPlaceholders: string[]): {
  isValid: boolean;
  missingPlaceholders: string[];
} {
  const templatePlaceholders = getTemplatePlaceholders(template.templateHtml);
  const missingPlaceholders = requiredPlaceholders.filter(
    placeholder => !templatePlaceholders.includes(placeholder)
  );

  return {
    isValid: missingPlaceholders.length === 0,
    missingPlaceholders
  };
}
