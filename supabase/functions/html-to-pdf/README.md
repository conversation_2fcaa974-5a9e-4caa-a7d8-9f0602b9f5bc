# HTML to PDF Edge Function

This Supabase Edge Function converts HTML content to PDF using Playwright and browserless.io.

## Migration from Next.js API Route

This function replaces the previous Next.js API route (`web/app/api/html-to-pdf/route.ts`) that used Puppeteer with local Chromium binaries. The new implementation:

- Uses **Playwright** instead of Puppeteer for better performance and reliability
- Connects to **browserless.io** cloud service instead of running local Chromium
- Runs as a **Supabase Edge Function** for global distribution and better scalability
- Eliminates the need for large Chromium binaries in your deployment

## Setup

### 1. Get browserless.io API Token

1. Visit [browserless.io](https://www.browserless.io/)
2. Sign up for a free account
3. Get your API token from the dashboard

### 2. Set Environment Variable

Add your browserless.io token to your Supabase project:

```bash
# Using Supabase CLI
supabase secrets set BROWSERLESS_IO_TOKEN=your_token_here

# Or via Supabase Dashboard
# Go to Project Settings > Edge Functions > Environment Variables
# Add: BROWSERLESS_IO_TOKEN = your_token_here
```

### 3. Deploy the Function

```bash
# Deploy to Supabase
supabase functions deploy html-to-pdf

# Or deploy all functions
supabase functions deploy
```

## Usage

### Request

```typescript
POST https://your-project.supabase.co/functions/v1/html-to-pdf

// Headers
{
  "Content-Type": "application/json",
  "Authorization": "Bearer YOUR_ANON_KEY"
}

// Body
{
  "html": "<html><body><h1>Hello World</h1></body></html>",
  "fileName": "my-document" // optional, defaults to "document"
}
```

### Response

- **Success (200)**: Returns PDF binary data with appropriate headers
- **Error (400)**: Missing or invalid HTML content
- **Error (500)**: PDF generation failed

### Example Usage in Frontend

```typescript
const generatePDF = async (htmlContent: string, fileName?: string) => {
  const response = await fetch('/functions/v1/html-to-pdf', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY}`,
    },
    body: JSON.stringify({
      html: htmlContent,
      fileName: fileName || 'document',
    }),
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Failed to generate PDF')
  }

  // Get PDF blob
  const pdfBlob = await response.blob()
  
  // Create download link
  const url = URL.createObjectURL(pdfBlob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${fileName || 'document'}.pdf`
  a.click()
  URL.revokeObjectURL(url)
}
```

## Features

- **A4 Format**: PDF generated in A4 format with proper dimensions
- **Print Background**: CSS backgrounds and colors are included
- **Font Loading**: Waits for web fonts to load before generating PDF
- **Network Idle**: Waits for network requests to complete
- **Error Handling**: Comprehensive error handling with detailed logging
- **Global Distribution**: Runs at the edge for low latency worldwide

## Differences from Previous Implementation

| Feature | Previous (Next.js + Puppeteer) | New (Edge Function + Playwright) |
|---------|--------------------------------|-----------------------------------|
| Runtime | Node.js with Chromium binary | Deno with browserless.io |
| Bundle Size | Large (~100MB+ with Chromium) | Small (~1MB) |
| Cold Start | Slow (binary loading) | Fast (WebSocket connection) |
| Scalability | Limited by server resources | Auto-scaling cloud service |
| Maintenance | Chromium binary updates needed | Managed by browserless.io |
| Global Distribution | Single region | Edge locations worldwide |

## Troubleshooting

### Common Issues

1. **"Server configuration error"**
   - Make sure `BROWSERLESS_IO_TOKEN` environment variable is set
   - Verify the token is valid on browserless.io dashboard

2. **"Failed to generate PDF"**
   - Check if HTML content is valid
   - Verify browserless.io service is accessible
   - Check function logs for detailed error messages

3. **Timeout errors**
   - Large HTML content may take longer to process
   - Consider optimizing HTML/CSS for faster rendering
   - Check browserless.io plan limits

### Debugging

View function logs:
```bash
supabase functions logs html-to-pdf
```

Test locally:
```bash
supabase functions serve html-to-pdf
```

## Cost Considerations

- **browserless.io**: Free tier available, paid plans for higher usage
- **Supabase Edge Functions**: Pay per invocation and execution time
- **Bandwidth**: PDF file size affects response time and costs

## Security

- API token is stored securely in Supabase environment variables
- HTML content is processed in isolated browserless.io containers
- No persistent storage of generated PDFs
