import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { chromium } from 'https://deno.land/x/playwright@1.40.0/mod.ts'

interface RequestBody {
  html: string
  fileName?: string
}

interface ErrorResponse {
  error: string
}

serve(async (req: Request): Promise<Response> => {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )
  }

  try {
    // Parse request body
    const body: RequestBody = await req.json()
    const { html, fileName = 'document' } = body

    if (!html) {
      return new Response(
        JSON.stringify({ error: 'HTML content is required' } as ErrorResponse),
        { 
          status: 400, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    // Get browserless.io API token from environment variables
    const browserlessToken = Deno.env.get('BROWSERLESS_IO_TOKEN')
    if (!browserlessToken) {
      console.error('BROWSERLESS_IO_TOKEN environment variable is not set')
      return new Response(
        JSON.stringify({ error: 'Server configuration error' } as ErrorResponse),
        { 
          status: 500, 
          headers: { 'Content-Type': 'application/json' } 
        }
      )
    }

    // Connect to browserless.io using Playwright
    console.log('Connecting to browserless.io...')
    const browser = await chromium.connect({
      wsEndpoint: `wss://chrome.browserless.io/playwright?token=${browserlessToken}`,
    })

    console.log('Creating new page...')
    const page = await browser.newPage()

    // Set viewport for consistent rendering
    await page.setViewportSize({
      width: 794,  // A4 width in pixels (72 dpi)
      height: 1123, // A4 height in pixels (72 dpi)
    })

    console.log('Setting HTML content...')
    // Set the HTML content
    await page.setContent(html, {
      waitUntil: 'networkidle', // Wait until network is idle
    })

    // Wait for fonts to be loaded
    await page.evaluate(() => document.fonts.ready)

    console.log('Generating PDF...')
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
      },
    })

    console.log('Closing browser...')
    // Close browser connection
    await browser.close()

    console.log('PDF generated successfully')
    // Return PDF as response
    return new Response(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${fileName}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    })

  } catch (error) {
    console.error('PDF generation error:', error)
    
    // Return detailed error information for debugging
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
    const errorStack = error instanceof Error ? error.stack : undefined
    
    console.error('Error details:', {
      message: errorMessage,
      stack: errorStack,
    })

    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate PDF',
        details: errorMessage 
      } as ErrorResponse),
      { 
        status: 500, 
        headers: { 'Content-Type': 'application/json' } 
      }
    )
  }
})
